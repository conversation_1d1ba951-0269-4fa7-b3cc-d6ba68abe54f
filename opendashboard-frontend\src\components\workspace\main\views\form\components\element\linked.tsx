import {TableViewTagEditor, TagItem} from "@/components/workspace/main/views/table/renderer/common/tag";
import React, {useEffect} from "react";
import {ManageFieldsProps} from "@/components/workspace/main/views/form/components/formContainer";
import {Label} from "@/components/ui/label";
import {Switch} from "@/components/ui/switch";
import {DatabaseFieldDataType, LinkedColumn, Match, RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {useWorkspace} from "@/providers/workspace";
import {Database, Record} from "@/typings/database";
import {LinkedTagItemRender, LinkedTagSelectionItemRender} from "@/components/workspace/main/views/table/renderer/fields/linked";
import {FormFieldAreaProps} from "@/components/workspace/main/views/form/components/element/text";
import {useMaybeRecord} from "@/providers/record";
import {DatabaseRecordStoreItem} from "@/typings/utilities";
import {getCompanyDbDefinition, getCustomerDbDefinition, getDatabasePackageName} from "opendb-app-db-utils/lib/utils/onboarding";
import {ViewFilter} from "@/components/workspace/main/views/common/viewFilter";
import {Button} from "@/components/ui/button";
import {FilterListIcon} from "@/components/icons/FontAwesomeRegular";
import {filterAndSortRecords} from "@/components/workspace/main/views/table";
import {useViews} from "@/providers/views";
import { getRecordTitle as getRecordTitleFromFormatter } from "@/utils/titleFormatter";

export const LinkedFieldArea = (props: FormFieldAreaProps) => {
    const {id, columnsMap, values, updateValues} = props

    const {databaseStore, databaseErrorStore, members, workspace} = useWorkspace()
    const {requestFullAdjacentDatabase} = useViews()

    const column = columnsMap[id] as LinkedColumn
    const columnProps = props.columnProps

    let rawDefaultValue = values[id] || columnProps.defaultValue
    let defaultValue: string[] = rawDefaultValue && Array.isArray(rawDefaultValue) && rawDefaultValue.length > 0 && typeof rawDefaultValue[0] === "string" ? rawDefaultValue as string[] : []

    let rawSelectableOptions = columnProps.selectableOptions
    let selectableOptions = rawSelectableOptions && Array.isArray(rawSelectableOptions) ? rawSelectableOptions : []
    const limitToOptions = columnProps.limitToOptions

    const recordContext = useMaybeRecord()

    const linkedDbStore = databaseStore[column.databaseId]
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const updatedAt = linkedDbStore ? linkedDbStore.updatedAt : new Date(0)

    let error = ''
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const filter = columnProps.selectableOptionsFilter || {conditions: [], match: Match.All}
    const limitByFilter = columnProps.limitByFilter

    const getSelectOptions = (): {
        items: TagItem<Record>[],
        updatedAt: string
    } => {
        if (!linkedDbStore) return {items: [], updatedAt: ''}
        const items = getLinkedTagItems(linkedDbStore, updatedAt, error, members)
        if (limitByFilter) {
            const {rows} = filterAndSortRecords(
                linkedDbStore,
                members,
                databaseStore,
                filter,
                {conditions: [], match: Match.All},
                [],
                workspace.workspaceMember.userId
            )
            const rowIds = rows.map(r => r.id)
            items.items = items.items.filter(i => rowIds.includes(i.id))
        }

        return items
    }
    const selOptions: {
        items: TagItem<Record>[],
        updatedAt: string
    } = getSelectOptions()

    const options = limitToOptions ? selOptions.items.filter(o => selectableOptions.includes(o.id)) : selOptions.items
    const allowMultiple = recordContext ? column.isMulti : columnProps.allowMultiple

    useEffect(() => {
        if (linkedDbStore && !linkedDbStore.recordsLoaded) {
            requestFullAdjacentDatabase(linkedDbStore.database.id, props.databaseId)
        }
    })

    return <>
        <TableViewTagEditor
            selectedIds={defaultValue}
            options={options}
            defaultOpen={false}
            onChange={s => {
                const cv: RecordValues = {}
                cv[id] = s
                updateValues(cv)
            }}
            itemRender={(key, index, item) => {
                return <LinkedTagItemRender item={item} key={key} multi={column.isMulti}/>
            }}
            itemSelectionRender={(key, index, item, isSelected, handleSelection) =>
                <LinkedTagSelectionItemRender item={item}
                                              onSelect={(select) => handleSelection(item.id, select)}
                                              isSelected={isSelected}
                                              key={key}
                                              multi
                />}
            disabled={props.disabled}
            className="h-8 border shadow-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full p-2 rounded-none text-xs border-neutral-300"
            multi={allowMultiple}/>
    </>
}

export const LinkedFieldPanel = (props: ManageFieldsProps) => {
    const {activeField, columnsMap, updateFieldProps} = props

    const {databaseStore, databaseErrorStore, members, workspace} = useWorkspace()


    const column = columnsMap[activeField] as LinkedColumn
    const columnProps = props.columnsPropMap[activeField]

    const linkedDbStore = databaseStore[column.databaseId]
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const updatedAt = linkedDbStore ? linkedDbStore.updatedAt : new Date(0)

    let error = ''


    let rawSelectableOptions = columnProps.selectableOptions
    let selectableOptions = rawSelectableOptions && Array.isArray(rawSelectableOptions) ? rawSelectableOptions : []


    const filter = columnProps.selectableOptionsFilter || {conditions: [], match: Match.All}
    const limitByFilter = columnProps.limitByFilter
    const limitToOptions = columnProps.limitToOptions

    const getSelectOptions = (): {
        items: TagItem<Record>[],
        updatedAt: string
    } => {
        const items = getLinkedTagItems(linkedDbStore, updatedAt, error, members)
        if (limitByFilter) {
            const {rows} = filterAndSortRecords(
                linkedDbStore,
                members,
                databaseStore,
                filter,
                {conditions: [], match: Match.All},
                [],
                workspace.workspaceMember.userId
            )
            const rowIds = rows.map(r => r.id)
            items.items = items.items.filter(i => rowIds.includes(i.id))
        }

        return items
    }
    const selOptions: {
        items: TagItem<Record>[],
        updatedAt: string
    } = getSelectOptions()

    const defaultOptions = limitToOptions ? selOptions.items.filter(o => selectableOptions.includes(o.id)) : selOptions.items

    let rawDefaultValue = columnProps.defaultValue
    let defaultValue: string[] = rawDefaultValue && Array.isArray(rawDefaultValue) && rawDefaultValue.length > 0 && typeof rawDefaultValue[0] === "string" ? rawDefaultValue as string[] : []

    return <>
        <header className="py-4 pb-0 flex gap-2">
            <div className="text-xs font-semibold">
                Linked Options
            </div>
        </header>
        <div className="flex gap-1 items-center">
            <Label htmlFor="name"
                   className="text-xs text-neutral-500 flex-1 overflow-hidden truncate">
                Limit selection by filter</Label>
            <Switch
                checked={!!columnProps.limitByFilter}
                onCheckedChange={limitByFilter => updateFieldProps(activeField, {limitByFilter})}
                aria-readonly
            />
        </div>
        {columnProps.limitByFilter && linkedDbStore.database && <div className='w-full max-w-full'>
            <ViewFilter
                database={linkedDbStore.database}
                trigger={
                    <Button variant="outline"
                            className="text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                        <FilterListIcon className="size-3"/>
                        {filter.conditions.length > 0 ?
                         `${filter.conditions.length} filters` :
                         'Filter'}
                    </Button>
                }
                filter={filter}
                onChange={selectableOptionsFilter => updateFieldProps(activeField, {selectableOptionsFilter})}
            />
        </div>}
        <div className="flex gap-1 items-center">
            <Label htmlFor="name"
                   className="text-xs text-neutral-500 flex-1 overflow-hidden truncate">
                Limit selection to options</Label>
            <Switch
                checked={!!columnProps.limitToOptions}
                onCheckedChange={limitToOptions => updateFieldProps(activeField, {limitToOptions})}
                aria-readonly
            />
        </div>
        {columnProps.limitToOptions && <div className='w-full max-w-full'>
            <Label className="text-xs text-neutral-500">Options:</Label>
            <TableViewTagEditor
                selectedIds={selectableOptions}
                options={selOptions.items}
                defaultOpen={false}
                className="h-8 border shadow-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full overflow-hidden max-w-full p-2 rounded-none text-xs border-neutral-300"
                onChange={selectableOptions => updateFieldProps(activeField, {selectableOptions})}
                itemRender={(key, index, item) => {
                    return <LinkedTagItemRender item={item} key={key} multi={column.isMulti}/>
                }}
                itemSelectionRender={(key, index, item, isSelected, handleSelection) =>
                    <LinkedTagSelectionItemRender item={item}
                                                  onSelect={(select) => handleSelection(item.id, select)}
                                                  isSelected={isSelected}
                                                  key={key}
                                                  multi
                    />}
                multi/>
        </div>}

        <div className="flex gap-1 items-center">
            <Label htmlFor="name"
                   className="text-xs text-neutral-500 flex-1 overflow-hidden truncate">
                Allow multiple</Label>
            <Switch
                checked={!!columnProps.allowMultiple}
                onCheckedChange={allowMultiple => updateFieldProps(activeField, {allowMultiple})}
                aria-readonly
            />
        </div>

        <div>
            <Label className="text-xs text-neutral-500">Default Value:</Label>
            <TableViewTagEditor
                selectedIds={defaultValue}
                options={defaultOptions}
                defaultOpen={false}
                className="h-8 border shadow-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full p-2 rounded-none text-xs border-neutral-300"
                onChange={defaultValue => updateFieldProps(activeField, {defaultValue})}
                itemRender={(key, index, item) => {
                    return <LinkedTagItemRender item={item} key={key} multi={column.isMulti}/>
                }}
                itemSelectionRender={(key, index, item, isSelected, handleSelection) =>
                    <LinkedTagSelectionItemRender item={item}
                                                  onSelect={(select) => handleSelection(item.id, select)}
                                                  isSelected={isSelected}
                                                  key={key}
                                                  multi
                    />}
                multi={!!columnProps.allowMultiple}/>
        </div>
    </>
}


const companyDef = getCompanyDbDefinition()
const contactsDef = getCustomerDbDefinition('')

const companySrcPackageName = getDatabasePackageName(companyDef)
const contactsSrcPackageName = getDatabasePackageName(contactsDef)


export const getDatabaseTitleCol = (database: Database) => {
    let defaultTitle = false
    let isContacts = false
    let titleColId = database.definition.titleColumnId || ''


    isContacts = database.srcPackageName === contactsSrcPackageName

    if (database.srcPackageName === companySrcPackageName && !titleColId) {
        titleColId = 'name'
    } else if (isContacts && !titleColId) {
        titleColId = 'firstName'
        defaultTitle = true
    }
    if (!titleColId || !database.definition.columnsMap[titleColId]) {
        for (const col of Object.values(database.definition.columnsMap)) {
            if (col.type === DatabaseFieldDataType.Text) {
                titleColId = col.id
                break
            }
        }
    }
    return {defaultTitle, isContacts, titleColId}
}

// export const getRecordTitle = (record: Record, titleColId: string, defaultTitle: boolean | string, isContacts: boolean, database?: any, members?: any[]) => {
//     return getRecordTitleFromFormatter(
//         record,
//         titleColId,
//         typeof defaultTitle === 'string' ? defaultTitle : 'Untitled',
//         isContacts,
//         database,
//         members
//     );
// }

export const getRecordTitle = (record: Record, titleColId: string, defaultTitle: boolean, isContacts: boolean) => {
    return record.title ? record.title :
           isContacts && defaultTitle ? `${String(record.recordValues['lastName'] || '')} ${String(record.recordValues['firstName'] || '')}`.trim() :
           record.recordValues[titleColId] && typeof record.recordValues[titleColId] === 'string' ? String(record.recordValues[titleColId]) :
           'Untitled'
}

export const getLinkedTagItems = (linkedDbStore?: DatabaseRecordStoreItem, updatedAt?: Date, error?: string, members?: any[]): {
    items: TagItem<Record>[],
    updatedAt: string,
    titleColId: string,
    defaultTitle: boolean
    isContacts: boolean
} => {
    let defaultTitle = false
    let isContacts = false
    let titleColId = ''

    const items: TagItem<Record>[] = []

    if (error) return {items, updatedAt: '', titleColId, defaultTitle, isContacts}
    if (!linkedDbStore) return {items, updatedAt: '', titleColId, defaultTitle, isContacts}


    const db = linkedDbStore

    const titleCol = getDatabaseTitleCol(db.database)
    defaultTitle = titleCol.defaultTitle
    isContacts = titleCol.isContacts
    titleColId = titleCol.titleColId

    for (const r of Object.values(db.recordsIdMap)) {
        const title: string = getRecordTitle(r.record, titleColId, defaultTitle, isContacts, db.database, members)

        const item: TagItem<Record> = {
            id: r.record.id,
            value: r.record.id,
            title,
            color: "LightGray",
            data: r.record
        }
        items.push(item)
    }
    return {items, updatedAt: updatedAt?.toISOString() || '', titleColId, defaultTitle, isContacts}
}

export const getLinkedToCreateRecordValues = (name: string, titleId: string, defaultTitle?: boolean, isContacts?: boolean) => {
    let values: RecordValues = {}
    if (defaultTitle && isContacts) {
        const split = name.trim().split(/\s+/); // Split by any whitespace (handles multiple spaces as well)
        const lastName = split[0];
        const firstName = split.slice(1).join(', ');

        values = {firstName, lastName}
    } else {
        values[titleId] = name
    }
    return values
}